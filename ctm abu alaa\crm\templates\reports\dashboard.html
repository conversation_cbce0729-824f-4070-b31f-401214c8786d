{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}التقارير{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i>طباعة
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-1"></i>تصدير Excel
            </button>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي العملاء
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي الديون
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_debts|currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المبلغ المدفوع
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_paid|currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                المبلغ المتبقي
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ remaining_debt|currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <!-- رسم بياني للديون الشهرية -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الديون الشهرية</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="monthlyDebtsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسم بياني دائري للحالات -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">حالة الديون</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="debtStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جداول التقارير -->
    <div class="row">
        <!-- أكبر الديون -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أكبر الديون</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for debt in top_debts %}
                                <tr>
                                    <td>{{ debt.customer.name }}</td>
                                    <td>{{ debt.amount|currency }}</td>
                                    <td>
                                        {% if debt.status == 'active' %}
                                            <span class="badge bg-warning">نشط</span>
                                        {% elif debt.status == 'paid' %}
                                            <span class="badge bg-success">مسدد</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ debt.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center text-muted">لا توجد ديون</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- الديون المتأخرة -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">الديون المتأخرة</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for debt in overdue_debts %}
                                <tr>
                                    <td>{{ debt.customer.name }}</td>
                                    <td>{{ debt.remaining_amount|currency }}</td>
                                    <td>{{ debt.due_date|date:"Y-m-d" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center text-muted">لا توجد ديون متأخرة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للديون الشهرية
const monthlyCtx = document.getElementById('monthlyDebtsChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: {{ monthly_labels|safe }},
        datasets: [{
            label: 'الديون الشهرية',
            data: {{ monthly_data|safe }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'الديون الشهرية'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني دائري لحالة الديون
const statusCtx = document.getElementById('debtStatusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['نشط', 'مسدد', 'ملغي'],
        datasets: [{
            data: {{ status_data|safe }},
            backgroundColor: [
                '#f6c23e',
                '#1cc88a',
                '#e74a3b'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportToExcel() {
    // تصدير البيانات إلى Excel
    window.location.href = '{% url "reports_export" %}';
}
</script>
{% endblock %}
