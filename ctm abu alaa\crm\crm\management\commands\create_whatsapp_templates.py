from django.core.management.base import BaseCommand
from crm.models import WhatsAppTemplate


class Command(BaseCommand):
    help = 'إنشاء قوالب رسائل WhatsApp افتراضية'

    def handle(self, *args, **options):
        templates = [
            {
                'name': 'تذكير بالدفع - عام',
                'template_type': 'payment_reminder',
                'content': '''السلام عليكم ورحمة الله وبركاته

عزيزي/عزيزتي {customer_name}

نتشرف بتذكيركم بوجود مبلغ {remaining_debt} مستحق للدفع.

نرجو منكم التكرم بتسديد المبلغ في أقرب وقت ممكن.

شكراً لتعاملكم معنا
{company_name}
للتواصل: {phone}''',
                'variables': '{customer_name}, {company_name}, {remaining_debt}, {phone}'
            },
            {
                'name': 'إشعار تأخير في الدفع',
                'template_type': 'overdue_notice',
                'content': '''السلام عليكم ورحمة الله وبركاته

عزيزي/عزيزتي {customer_name}

نود إعلامكم بأن المبلغ المستحق {overdue_amount} قد تجاوز تاريخ الاستحقاق.

نرجو منكم المبادرة بالتسديد لتجنب أي رسوم إضافية.

إجمالي المبلغ المتبقي: {remaining_debt}

نقدر تفهمكم وتعاونكم
{company_name}
للتواصل: {phone}''',
                'variables': '{customer_name}, {company_name}, {remaining_debt}, {overdue_amount}, {phone}'
            },
            {
                'name': 'تأكيد استلام دفعة',
                'template_type': 'payment_received',
                'content': '''السلام عليكم ورحمة الله وبركاته

عزيزي/عزيزتي {customer_name}

نشكركم على تسديد الدفعة الأخيرة.

تم استلام المبلغ بنجاح ✅

نقدر ثقتكم وتعاملكم معنا
{company_name}
للتواصل: {phone}''',
                'variables': '{customer_name}, {company_name}, {phone}'
            },
            {
                'name': 'رسالة ترحيب للعملاء الجدد',
                'template_type': 'general',
                'content': '''السلام عليكم ورحمة الله وبركاته

أهلاً وسهلاً بك {customer_name}

نرحب بك في عائلة عملاء {company_name}

نتطلع لخدمتك وتقديم أفضل ما لدينا

للتواصل معنا: {phone}

مرحباً بك مرة أخرى 🌟''',
                'variables': '{customer_name}, {company_name}, {phone}'
            },
            {
                'name': 'تذكير لطيف بالدفع',
                'template_type': 'payment_reminder',
                'content': '''مرحباً {customer_name} 😊

نأمل أن تكون بخير

هذا تذكير لطيف بوجود مبلغ {remaining_debt} مستحق للدفع

نقدر ظروفكم ونتفهم أي تأخير، يرجى التواصل معنا إذا كان هناك أي استفسار

شكراً لكم
{company_name} 💙''',
                'variables': '{customer_name}, {company_name}, {remaining_debt}'
            },
            {
                'name': 'إشعار عاجل - دين متأخر',
                'template_type': 'overdue_notice',
                'content': '''⚠️ إشعار عاجل ⚠️

عزيزي {customer_name}

المبلغ المستحق {overdue_amount} متأخر عن موعد الاستحقاق

إجمالي المبلغ المتبقي: {remaining_debt}

يرجى التسديد فوراً لتجنب اتخاذ الإجراءات القانونية

{company_name}
{phone}''',
                'variables': '{customer_name}, {company_name}, {remaining_debt}, {overdue_amount}, {phone}'
            },
            {
                'name': 'شكر وتقدير',
                'template_type': 'general',
                'content': '''السلام عليكم {customer_name}

نتقدم بجزيل الشكر والتقدير لكم على التزامكم بالدفع في المواعيد المحددة

عملاء مثلكم هم سر نجاحنا واستمراريتنا

نقدر ثقتكم الغالية بنا

{company_name}
{phone}

شكراً لكم من القلب ❤️''',
                'variables': '{customer_name}, {company_name}, {phone}'
            },
            {
                'name': 'عرض خاص للعملاء المميزين',
                'template_type': 'general',
                'content': '''🎉 عرض خاص 🎉

عزيزي {customer_name}

بمناسبة كونك من عملائنا المميزين، نقدم لك عرضاً خاصاً

تواصل معنا لمعرفة التفاصيل

{company_name}
{phone}

نقدر ولاءك وثقتك 🌟''',
                'variables': '{customer_name}, {company_name}, {phone}'
            }
        ]

        created_count = 0
        for template_data in templates:
            template, created = WhatsAppTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'تم إنشاء القالب: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'القالب موجود مسبقاً: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_count} قالب جديد من أصل {len(templates)} قالب')
        )
