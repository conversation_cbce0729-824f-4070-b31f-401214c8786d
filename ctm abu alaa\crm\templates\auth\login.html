<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - محلات أبو علاء</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: radial-gradient(circle at 20% 50%, #1a1a1a 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, #2a2a2a 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, #1a1a1a 0%, transparent 50%);
            animation: bgMove 20s ease-in-out infinite;
        }

        @keyframes bgMove {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-20px) translateY(-10px); }
            50% { transform: translateX(20px) translateY(10px); }
            75% { transform: translateX(-10px) translateY(20px); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(255,255,255,0.1);
            overflow: hidden;
            max-width: 420px;
            width: 100%;
            position: relative;
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* شعار العراق متحرك في الخلفية */
        .animated-logo {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0.08;
            z-index: -1;
            width: 400px;
            height: 500px;
            animation: logoFloat 8s ease-in-out infinite;
        }

        .animated-logo svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translate(-50%, -50%) rotate(-2deg) scale(0.9);
                opacity: 0.08;
            }
            50% {
                transform: translate(-50%, -50%) rotate(2deg) scale(1);
                opacity: 0.12;
            }
        }

        .watermark-login {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-15deg);
            opacity: 0.1;
            z-index: 0;
            max-width: 250px;
            animation: watermarkPulse 4s ease-in-out infinite;
        }

        @keyframes watermarkPulse {
            0%, 100% { opacity: 0.1; transform: translate(-50%, -50%) rotate(-15deg) scale(1); }
            50% { opacity: 0.15; transform: translate(-50%, -50%) rotate(-10deg) scale(1.05); }
        }

        .developer-credit {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 11px;
            color: #667eea;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .login-header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            color: white;
            padding: 35px;
            text-align: right;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 120px;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .login-header h2 {
            margin: 0;
            font-weight: 700;
            font-size: 1.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .login-header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .login-body {
            padding: 45px 35px;
            text-align: right;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.4s ease;
            text-align: right;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            border-color: #ffd700;
            box-shadow: 0 0 0 0.3rem rgba(255, 215, 0, 0.25);
            background: rgba(255,255,255,1);
            transform: translateY(-2px);
        }

        .btn-login {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            border: none;
            border-radius: 15px;
            padding: 15px;
            font-weight: 700;
            font-size: 1.2rem;
            width: 100%;
            color: #1a1a1a;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
            text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #ffb347 0%, #ffd700 100%);
            transform: translateY(-3px);
            color: #1a1a1a;
            box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
        }

        .form-floating label {
            color: #6c757d;
            font-weight: 600;
            text-align: right;
            right: 20px;
            left: auto;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: #ffd700;
            font-weight: 700;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
        }
        
        .logo-section {
            text-align: right;
            margin-bottom: 25px;
            position: relative;
        }

        .logo-section svg {
            width: 80px;
            height: 100px;
            margin-bottom: 15px;
            filter: drop-shadow(2px 2px 8px rgba(255, 215, 0, 0.5));
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            from {
                filter: drop-shadow(2px 2px 8px rgba(255, 215, 0, 0.5));
                transform: scale(1);
            }
            to {
                filter: drop-shadow(2px 2px 15px rgba(255, 215, 0, 0.8));
                transform: scale(1.05);
            }
        }

        /* تأثيرات إضافية */
        .alert {
            border: none;
            border-radius: 15px;
            text-align: right;
        }

        .text-center {
            text-align: right !important;
        }

        .developer-credit {
            background: rgba(255, 215, 0, 0.9);
            color: #1a1a1a;
            font-weight: 600;
        }

        /* تنسيق اللوغو الكبير في صفحة تسجيل الدخول */
        .logo-section {
            position: relative;
            z-index: 10;
        }

        .login-big-logo {
            width: 200px !important;
            height: 200px !important;
            filter: drop-shadow(0 8px 30px rgba(255,215,0,0.8));
            transition: all 0.5s ease;
            animation: logoFloatBig 4s ease-in-out infinite;
        }

        .login-big-logo:hover {
            transform: scale(1.15) rotate(10deg);
            filter: drop-shadow(0 12px 40px rgba(255,215,0,1));
        }

        @keyframes logoFloatBig {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                box-shadow:
                    0 0 10px rgba(255,215,0,0.5),
                    0 0 20px rgba(255,215,0,0.3);
                border-color: #FFD700;
            }
            25% {
                transform: translateY(-3px) rotate(1deg);
                box-shadow:
                    0 0 12px rgba(255,215,0,0.6),
                    0 0 25px rgba(255,215,0,0.4);
                border-color: #FFA500;
            }
            50% {
                transform: translateY(-5px) rotate(0deg);
                box-shadow:
                    0 0 15px rgba(255,215,0,0.7),
                    0 0 30px rgba(255,215,0,0.5);
                border-color: #FF8C00;
            }
            75% {
                transform: translateY(-3px) rotate(-1deg);
                box-shadow:
                    0 0 12px rgba(255,215,0,0.6),
                    0 0 25px rgba(255,215,0,0.4);
                border-color: #FFA500;
            }
        }

        /* تنسيق النصوص في الرأس */
        .header-text {
            flex: 1;
            text-align: right;
            padding-right: 30px;
        }

        /* اللوغو الكبير CSS */
        .css-logo-big {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 100px;
            background: #1a1a1a;
            border-radius: 50%;
            box-shadow:
                0 0 10px rgba(255,215,0,0.5),
                0 0 20px rgba(255,215,0,0.3);
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
            animation: logoFloatBig 4s ease-in-out infinite;
            border: 2px solid #FFD700;
        }

        .css-logo-big:hover {
            transform: scale(1.05);
            box-shadow:
                0 0 15px rgba(255,215,0,0.7),
                0 0 25px rgba(255,215,0,0.4);
            border-color: #FFA500;
        }

        .crown-big {
            font-size: 16px;
            line-height: 1;
            margin-bottom: 2px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
            animation: crownBounce 2s ease-in-out infinite;
            color: #FFD700;
        }

        @keyframes crownBounce {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-2px) rotate(2deg); }
        }

        .letters-big {
            font-family: 'Times New Roman', serif;
            font-weight: 900;
            font-size: 10px;
            color: #FFD700;
            text-shadow:
                1px 1px 1px rgba(0,0,0,0.8),
                0 0 5px rgba(255,215,0,0.4);
            line-height: 1.1;
            direction: ltr !important;
            unicode-bidi: bidi-override;
            letter-spacing: 0.5px;
            animation: lettersGlow 3s ease-in-out infinite;
            text-align: center;
        }

        @keyframes lettersGlow {
            0%, 100% {
                text-shadow:
                    1px 1px 1px rgba(0,0,0,0.8),
                    0 0 5px rgba(255,215,0,0.4);
            }
            50% {
                text-shadow:
                    1px 1px 2px rgba(0,0,0,0.9),
                    0 0 8px rgba(255,215,0,0.6);
            }
        }

        .shine-effect {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(255,255,255,0.4) 50%,
                transparent 70%);
            animation: logoShine 3s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>

    <!-- شعار العراق متحرك في الخلفية -->
    <div class="animated-logo">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 500" width="400" height="500">
            <!-- Eagle Body -->
            <g id="eagle">
                <!-- Eagle Head -->
                <path d="M200 50 Q180 40 160 50 Q150 60 155 75 Q160 85 175 90 Q190 95 200 90 Q210 95 225 90 Q240 85 245 75 Q250 60 240 50 Q220 40 200 50 Z" fill="#FFD700" stroke="#000" stroke-width="2"/>

                <!-- Eagle Beak -->
                <path d="M155 75 Q140 80 130 85 Q125 90 130 95 Q140 90 155 85 Z" fill="#000"/>

                <!-- Eagle Eye -->
                <circle cx="170" cy="70" r="3" fill="#000"/>

                <!-- Eagle Body -->
                <ellipse cx="200" cy="150" rx="80" ry="60" fill="#FFD700" stroke="#000" stroke-width="2"/>

                <!-- Eagle Wings -->
                <path d="M120 120 Q80 100 40 120 Q20 140 30 180 Q40 220 80 240 Q120 220 140 180 Q130 140 120 120 Z" fill="#FFD700" stroke="#000" stroke-width="2"/>
                <path d="M280 120 Q320 100 360 120 Q380 140 370 180 Q360 220 320 240 Q280 220 260 180 Q270 140 280 120 Z" fill="#FFD700" stroke="#000" stroke-width="2"/>

                <!-- Wing Details -->
                <g stroke="#000" stroke-width="1" fill="none">
                    <path d="M50 140 Q70 150 90 160"/>
                    <path d="M55 160 Q75 170 95 180"/>
                    <path d="M60 180 Q80 190 100 200"/>
                    <path d="M350 140 Q330 150 310 160"/>
                    <path d="M345 160 Q325 170 305 180"/>
                    <path d="M340 180 Q320 190 300 200"/>
                </g>

                <!-- Eagle Talons -->
                <g fill="#FFD700" stroke="#000" stroke-width="2">
                    <ellipse cx="180" cy="220" rx="15" ry="8"/>
                    <ellipse cx="220" cy="220" rx="15" ry="8"/>
                </g>
            </g>

            <!-- Shield -->
            <g id="shield">
                <!-- Shield Outline -->
                <path d="M150 180 Q150 160 170 160 L230 160 Q250 160 250 180 L250 280 Q250 300 230 320 Q200 340 200 340 Q200 340 170 320 Q150 300 150 280 Z" fill="#FFF" stroke="#000" stroke-width="3"/>

                <!-- Iraqi Flag Colors -->
                <!-- Red stripe -->
                <rect x="160" y="170" width="80" height="25" fill="#CE1126"/>

                <!-- White stripe with text -->
                <rect x="160" y="195" width="80" height="25" fill="#FFF"/>

                <!-- Black stripe -->
                <rect x="160" y="220" width="80" height="25" fill="#000"/>

                <!-- Arabic Text "الله أكبر" (Allahu Akbar) -->
                <text x="200" y="210" text-anchor="middle" font-family="Arial" font-size="8" fill="#00A651" font-weight="bold">الله أكبر</text>
            </g>

            <!-- Banner at bottom -->
            <g id="banner">
                <path d="M100 380 Q100 370 110 370 L290 370 Q300 370 300 380 L300 400 Q300 410 290 410 L110 410 Q100 410 100 400 Z" fill="#00A651" stroke="#000" stroke-width="2"/>

                <!-- Arabic Text "جمهورية العراق" (Republic of Iraq) -->
                <text x="200" y="395" text-anchor="middle" font-family="Arial" font-size="12" fill="#FFF" font-weight="bold">جمهورية العراق</text>
            </g>

            <!-- Additional decorative elements -->
            <g id="decorations">
                <!-- Feather details on eagle body -->
                <g stroke="#000" stroke-width="1" fill="none">
                    <path d="M160 130 Q170 ***********"/>
                    <path d="M165 140 Q175 ***********"/>
                    <path d="M170 150 Q180 ***********"/>
                    <path d="M220 140 Q215 ***********"/>
                    <path d="M225 150 Q220 ***********"/>
                    <path d="M230 160 Q225 ***********"/>
                </g>
            </g>
        </svg>
    </div>

    <!-- العلامة المائية -->
    {% if company_settings.logo %}
        <div class="watermark-login">
            <img src="{{ company_settings.logo.url }}" alt="علامة مائية" class="img-fluid">
        </div>
    {% endif %}

    <div class="login-container">
        <div class="login-header">
            <!-- النصوص في الجانب الأيمن -->
            <div class="header-text">
                <h2>محلات أبو علاء</h2>
                <div class="subtitle">نظام إدارة الديون التجارية</div>
            </div>

            <!-- اللوغو الكبير في الجانب الأيسر -->
            <div class="logo-section">
                <!-- لوغو CSS كبير ومتحرك -->
                <div class="css-logo-big">
                    <div class="crown-big">👑</div>
                    <div class="letters-big">ABU<br>ALAA</div>
                    <div class="shine-effect"></div>
                </div>
            </div>
        </div>
        
        <div class="login-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post">
                {% csrf_token %}
                
                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                    <label for="username">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                </div>
                
                <div class="form-floating mb-4">
                    <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                </div>
                
                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="text-right mt-4">
                <small class="text-muted">
                    <i class="fas fa-shield-alt ms-1"></i>
                    نظام آمن ومحمي
                </small>
            </div>
        </div>
    </div>

    <!-- حقوق المبرمج -->
    <div class="developer-credit">
        <i class="fas fa-code me-1"></i>
        تطوير: المبرمج خالد شجاع © 2025
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
