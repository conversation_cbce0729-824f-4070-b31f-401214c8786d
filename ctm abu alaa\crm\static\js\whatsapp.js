/**
 * WhatsApp Integration JavaScript
 * محلات أبو علاء - نظام إدارة العملاء والديون
 */

// WhatsApp Utilities
const WhatsAppUtils = {
    // تنسيق رقم الهاتف لـ WhatsApp
    formatPhoneNumber: function(phone) {
        // إزالة المسافات والرموز الخاصة
        phone = phone.replace(/[\s\-\(\)]/g, '');
        
        // إضافة رمز الدولة إذا لم يكن موجوداً
        if (!phone.startsWith('+')) {
            if (phone.startsWith('07')) {  // أرقام عراقية
                phone = '+964' + phone.substring(1);
            } else if (phone.startsWith('7')) {
                phone = '+964' + phone;
            } else if (!phone.startsWith('964')) {
                phone = '+964' + phone;
            } else {
                phone = '+' + phone;
            }
        }
        
        return phone;
    },

    // إنشاء رابط WhatsApp
    createWhatsAppURL: function(phone, message) {
        const formattedPhone = this.formatPhoneNumber(phone);
        const encodedMessage = encodeURIComponent(message);
        return `https://wa.me/${formattedPhone.replace('+', '')}?text=${encodedMessage}`;
    },

    // فتح WhatsApp
    openWhatsApp: function(phone, message) {
        const url = this.createWhatsAppURL(phone, message);
        window.open(url, '_blank');
    },

    // نسخ رابط WhatsApp
    copyWhatsAppLink: function(phone, message) {
        const url = this.createWhatsAppURL(phone, message);
        return navigator.clipboard.writeText(url);
    },

    // استبدال المتغيرات في النص
    replaceVariables: function(text, variables) {
        let result = text;
        for (const [key, value] of Object.entries(variables)) {
            const regex = new RegExp(`{${key}}`, 'g');
            result = result.replace(regex, value);
        }
        return result;
    },

    // عرض Toast notification
    showToast: function(message, type = 'success') {
        // إنشاء Toast element
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-${type === 'success' ? 'check-circle text-success' : 'exclamation-triangle text-warning'} me-2"></i>
                    <strong class="me-auto">${type === 'success' ? 'نجح' : 'تنبيه'}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // إزالة Toast بعد إخفاؤه
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    },

    // إنشاء Toast container إذا لم يكن موجوداً
    createToastContainer: function() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    },

    // تأكيد الإجراء
    confirmAction: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    // فتح عدة روابط WhatsApp مع تأخير
    openMultipleLinks: function(links, delay = 1500) {
        if (links.length === 0) return;
        
        this.confirmAction(
            `هل تريد فتح ${links.length} رابط واتساب؟\nسيتم فتح كل رابط مع تأخير ${delay/1000} ثانية.`,
            () => {
                links.forEach((link, index) => {
                    setTimeout(() => {
                        window.open(link, '_blank');
                    }, index * delay);
                });
                this.showToast(`تم فتح ${links.length} رابط واتساب`);
            }
        );
    },

    // تصدير الروابط كملف نصي
    exportLinksAsText: function(data, filename) {
        const content = data.join('\n');
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        this.showToast('تم تصدير الملف بنجاح');
    },

    // إضافة تأثيرات بصرية للأزرار
    addButtonEffects: function() {
        document.querySelectorAll('.btn-whatsapp').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.05)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
            
            button.addEventListener('click', function() {
                this.style.transform = 'translateY(0) scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                }, 150);
            });
        });
    },

    // تهيئة الأحداث
    init: function() {
        // إضافة تأثيرات الأزرار
        this.addButtonEffects();
        
        // إضافة مستمعي الأحداث للأزرار العامة
        document.addEventListener('click', (e) => {
            if (e.target.matches('.whatsapp-quick-send')) {
                e.preventDefault();
                const phone = e.target.dataset.phone;
                const message = e.target.dataset.message || 'مرحباً';
                this.openWhatsApp(phone, message);
            }
            
            if (e.target.matches('.whatsapp-copy-link')) {
                e.preventDefault();
                const phone = e.target.dataset.phone;
                const message = e.target.dataset.message || 'مرحباً';
                this.copyWhatsAppLink(phone, message).then(() => {
                    this.showToast('تم نسخ رابط واتساب');
                });
            }
        });
        
        console.log('WhatsApp Utils initialized successfully');
    }
};

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    WhatsAppUtils.init();
});

// تصدير للاستخدام العام
window.WhatsAppUtils = WhatsAppUtils;
