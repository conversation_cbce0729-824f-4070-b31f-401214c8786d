{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}إرسال رسائل للمتأخرين - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .whatsapp-btn {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .whatsapp-btn:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .stats-card {
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 2px solid;
        transition: all 0.3s ease;
    }
    
    .stats-card.overdue {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-color: #dc3545;
    }
    
    .stats-card.due-soon {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
        border-color: #ffc107;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .target-selector {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #dee2e6;
        margin-bottom: 20px;
    }
    
    .target-option {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }
    
    .target-option:hover {
        border-color: #25D366;
        background-color: rgba(37, 211, 102, 0.1);
    }
    
    .target-option.selected {
        border-color: #25D366;
        background-color: rgba(37, 211, 102, 0.2);
    }
    
    .template-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .template-card:hover {
        border-color: #25D366;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
    }
    
    .template-card.selected {
        border-color: #25D366;
        background-color: rgba(37, 211, 102, 0.1);
    }
    
    .customer-preview {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 15px;
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
        إرسال رسائل للمتأخرين عن الدفع
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'debt_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للديون
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="stats-card overdue">
            <h3 class="mb-1">{{ overdue_count }}</h3>
            <p class="mb-0">عميل متأخر عن الدفع</p>
            <small>ديون متجاوزة تاريخ الاستحقاق</small>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card due-soon">
            <h3 class="mb-1">{{ due_soon_count }}</h3>
            <p class="mb-0">عميل مستحق خلال 7 أيام</p>
            <small>ديون قريبة من الاستحقاق</small>
        </div>
    </div>
</div>

<form method="post" id="overdueWhatsappForm">
    {% csrf_token %}
    
    <div class="row">
        <!-- اختيار الفئة المستهدفة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-target me-2"></i>اختيار الفئة المستهدفة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="target-selector">
                        <div class="target-option" data-target="overdue">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <input type="radio" name="target_type" value="overdue" id="targetOverdue" checked>
                                    <label for="targetOverdue" class="ms-2 mb-0">
                                        <strong>المتأخرين عن الدفع</strong>
                                    </label>
                                </div>
                                <span class="badge bg-danger">{{ overdue_count }}</span>
                            </div>
                            <small class="text-muted">العملاء الذين تجاوزوا تاريخ الاستحقاق</small>
                        </div>
                        
                        <div class="target-option" data-target="due_soon">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <input type="radio" name="target_type" value="due_soon" id="targetDueSoon">
                                    <label for="targetDueSoon" class="ms-2 mb-0">
                                        <strong>المستحقين قريباً</strong>
                                    </label>
                                </div>
                                <span class="badge bg-warning">{{ due_soon_count }}</span>
                            </div>
                            <small class="text-muted">العملاء المستحقين خلال 7 أيام</small>
                        </div>
                    </div>
                    
                    <!-- معاينة العملاء -->
                    <div class="mt-3">
                        <h6>معاينة العملاء المستهدفين:</h6>
                        <div class="customer-preview" id="customerPreview">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نموذج الرسالة -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fab fa-whatsapp me-2"></i>محتوى الرسالة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- اختيار القالب -->
                    {% if templates %}
                    <div class="mb-3">
                        <label class="form-label">قوالب جاهزة</label>
                        {% for template in templates %}
                        <div class="template-card" data-template-id="{{ template.id }}" data-content="{{ template.content }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ template.name }}</h6>
                                    <small class="text-muted">{{ template.get_template_type_display }}</small>
                                </div>
                                <span class="badge bg-secondary">{{ template.template_type }}</span>
                            </div>
                            <p class="mb-0 mt-2 small">{{ template.content|truncatewords:15 }}</p>
                        </div>
                        {% endfor %}
                        <input type="hidden" name="template_id" id="templateId">
                    </div>
                    {% endif %}
                    
                    <!-- محتوى الرسالة -->
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="messageContent" name="message_content" rows="8" 
                                  placeholder="اكتب رسالتك هنا..." required></textarea>
                        <div class="form-text">
                            <strong>المتغيرات المتاحة:</strong><br>
                            <code>{customer_name}</code> - اسم العميل<br>
                            <code>{company_name}</code> - اسم الشركة<br>
                            <code>{remaining_debt}</code> - إجمالي المبلغ المتبقي<br>
                            <code>{overdue_amount}</code> - المبلغ المتأخر<br>
                            <code>{phone}</code> - هاتف الشركة
                        </div>
                    </div>
                    
                    <!-- معاينة الرسالة -->
                    <div class="mb-3">
                        <label class="form-label">معاينة الرسالة</label>
                        <div class="border rounded p-3 bg-light" id="messagePreview" style="min-height: 100px;">
                            <em class="text-muted">ستظهر معاينة الرسالة هنا...</em>
                        </div>
                    </div>
                    
                    <!-- زر الإرسال -->
                    <div class="d-grid">
                        <button type="submit" class="whatsapp-btn" id="sendButton">
                            <i class="fab fa-whatsapp"></i>
                            إنشاء روابط واتساب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const targetOptions = document.querySelectorAll('.target-option');
    const targetRadios = document.querySelectorAll('input[name="target_type"]');
    const customerPreview = document.getElementById('customerPreview');
    const templateCards = document.querySelectorAll('.template-card');
    const templateIdInput = document.getElementById('templateId');
    const messageContent = document.getElementById('messageContent');
    const messagePreview = document.getElementById('messagePreview');
    const sendButton = document.getElementById('sendButton');
    
    // بيانات العملاء
    const overdueCustomers = [
        {% for customer in overdue_customers %}
        {
            name: '{{ customer.name }}',
            phone: '{{ customer.phone }}',
            remaining_debt: '{{ customer.remaining_debt|currency }}'
        },
        {% endfor %}
    ];
    
    const dueSoonCustomers = [
        {% for customer in due_soon_customers %}
        {
            name: '{{ customer.name }}',
            phone: '{{ customer.phone }}',
            remaining_debt: '{{ customer.remaining_debt|currency }}'
        },
        {% endfor %}
    ];
    
    // معالجة اختيار الفئة المستهدفة
    targetOptions.forEach(option => {
        option.addEventListener('click', function() {
            const targetType = this.dataset.target;
            const radio = document.getElementById(targetType === 'overdue' ? 'targetOverdue' : 'targetDueSoon');
            radio.checked = true;
            
            // تحديث التصميم
            targetOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            
            // تحديث معاينة العملاء
            updateCustomerPreview(targetType);
            
            // تحديث حالة الزر
            updateSendButton();
        });
    });
    
    // معالجة اختيار القالب
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            templateCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            
            templateIdInput.value = this.dataset.templateId;
            messageContent.value = this.dataset.content;
            
            updateMessagePreview();
        });
    });
    
    // تحديث معاينة الرسالة عند الكتابة
    messageContent.addEventListener('input', updateMessagePreview);
    
    // تحديث معاينة العملاء
    function updateCustomerPreview(targetType) {
        const customers = targetType === 'overdue' ? overdueCustomers : dueSoonCustomers;
        
        if (customers.length === 0) {
            customerPreview.innerHTML = '<p class="text-muted mb-0">لا يوجد عملاء في هذه الفئة</p>';
            return;
        }
        
        let html = '<div class="row">';
        customers.forEach(customer => {
            html += `
                <div class="col-12 mb-2">
                    <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                        <div>
                            <strong>${customer.name}</strong><br>
                            <small class="text-muted">${customer.phone}</small>
                        </div>
                        <span class="badge bg-warning">${customer.remaining_debt}</span>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        customerPreview.innerHTML = html;
    }
    
    // تحديث معاينة الرسالة
    function updateMessagePreview() {
        let content = messageContent.value;
        if (content) {
            // استبدال المتغيرات للمعاينة
            content = content
                .replace(/{customer_name}/g, 'أحمد محمد')
                .replace(/{company_name}/g, '{{ company_settings.company_name|default:"محلات أبو علاء" }}')
                .replace(/{remaining_debt}/g, '500,000 د.ع')
                .replace(/{overdue_amount}/g, '200,000 د.ع')
                .replace(/{phone}/g, '{{ company_settings.phone|default:"07XX XXX XXXX" }}');
            
            messagePreview.innerHTML = content.replace(/\n/g, '<br>');
        } else {
            messagePreview.innerHTML = '<em class="text-muted">ستظهر معاينة الرسالة هنا...</em>';
        }
    }
    
    // تحديث حالة زر الإرسال
    function updateSendButton() {
        const selectedTarget = document.querySelector('input[name="target_type"]:checked').value;
        const customers = selectedTarget === 'overdue' ? overdueCustomers : dueSoonCustomers;
        
        sendButton.disabled = customers.length === 0;
        
        if (customers.length === 0) {
            sendButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> لا يوجد عملاء';
        } else {
            sendButton.innerHTML = `<i class="fab fa-whatsapp"></i> إنشاء روابط لـ ${customers.length} عميل`;
        }
    }
    
    // التهيئة الأولية
    updateCustomerPreview('overdue');
    updateMessagePreview();
    updateSendButton();
    
    // تحديد الخيار الأول
    targetOptions[0].classList.add('selected');
});
</script>
{% endblock %}
