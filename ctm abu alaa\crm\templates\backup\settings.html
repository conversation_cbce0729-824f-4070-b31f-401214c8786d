{% extends 'base.html' %}

{% block title %}إعدادات النسخ الاحتياطي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cog me-2"></i>إعدادات النسخ الاحتياطي
        </h1>
        <a href="{% url 'backup_dashboard' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إعدادات النسخ التلقائي</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label class="form-label">تفعيل النسخ الاحتياطي التلقائي</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoBackupEnabled" 
                                       {% if backup_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="autoBackupEnabled">
                                    تفعيل النسخ التلقائي
                                </label>
                            </div>
                            <div class="form-text">
                                عند التفعيل، سيتم إنشاء نسخ احتياطية تلقائياً حسب الجدولة المحددة
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="backupFrequency" class="form-label">تكرار النسخ الاحتياطي</label>
                            <select class="form-select" id="backupFrequency" name="backup_frequency">
                                <option value="daily">يومي</option>
                                <option value="weekly" selected>أسبوعي</option>
                                <option value="monthly">شهري</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label for="backupTime" class="form-label">وقت النسخ الاحتياطي</label>
                            <input type="time" class="form-control" id="backupTime" name="backup_time" value="02:00">
                            <div class="form-text">
                                الوقت المفضل لإجراء النسخ الاحتياطي التلقائي
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="backupLocation" class="form-label">مكان حفظ النسخ</label>
                            <select class="form-select" id="backupLocation" name="backup_location">
                                <option value="local">محلي فقط</option>
                                <option value="google_drive" selected>Google Drive</option>
                                <option value="both">محلي + Google Drive</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label for="retentionDays" class="form-label">مدة الاحتفاظ بالنسخ (بالأيام)</label>
                            <input type="number" class="form-control" id="retentionDays" name="retention_days" 
                                   value="30" min="7" max="365">
                            <div class="form-text">
                                سيتم حذف النسخ الاحتياطية الأقدم من هذه المدة تلقائياً
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> لتفعيل النسخ التلقائي، يجب إعداد Cron Job أو Task Scheduler 
                            لتشغيل الأمر التالي:
                            <br>
                            <code>python manage.py auto_backup --type=google_drive --cleanup</code>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'backup_dashboard' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أوامر النسخ الاحتياطي</h6>
                </div>
                <div class="card-body">
                    <h6>أوامر Django المتاحة:</h6>
                    
                    <div class="mb-3">
                        <strong>نسخة احتياطية محلية:</strong>
                        <pre class="bg-light p-2 rounded small">python manage.py auto_backup --type=local</pre>
                    </div>
                    
                    <div class="mb-3">
                        <strong>نسخة احتياطية إلى Google Drive:</strong>
                        <pre class="bg-light p-2 rounded small">python manage.py auto_backup --type=google_drive</pre>
                    </div>
                    
                    <div class="mb-3">
                        <strong>نسخة احتياطية مع تنظيف:</strong>
                        <pre class="bg-light p-2 rounded small">python manage.py auto_backup --type=google_drive --cleanup</pre>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إعداد Cron Job</h6>
                </div>
                <div class="card-body">
                    <p class="small">لتشغيل النسخ الاحتياطي تلقائياً كل يوم في الساعة 2:00 صباحاً:</p>
                    
                    <strong>Linux/Mac:</strong>
                    <pre class="bg-light p-2 rounded small">0 2 * * * cd /path/to/project && python manage.py auto_backup --type=google_drive --cleanup</pre>
                    
                    <strong>Windows Task Scheduler:</strong>
                    <p class="small">أنشئ مهمة جديدة تشغل الأمر أعلاه يومياً في الوقت المحدد</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
