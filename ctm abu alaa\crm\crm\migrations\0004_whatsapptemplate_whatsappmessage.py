# Generated by Django 5.2.3 on 2025-07-13 08:10

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('crm', '0003_customer_page_number'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WhatsAppTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('template_type', models.CharField(choices=[('payment_reminder', 'تذكير بالدفع'), ('overdue_notice', 'إشعار تأخير'), ('payment_received', 'تأكيد استلام دفعة'), ('general', 'عام')], max_length=20, verbose_name='نوع القالب')),
                ('content', models.TextField(verbose_name='محتوى القالب')),
                ('variables', models.TextField(blank=True, help_text='مثل: {customer_name}, {amount}, {due_date}, {company_name}', null=True, verbose_name='المتغيرات المتاحة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قالب رسالة واتساب',
                'verbose_name_plural': 'قوالب رسائل الواتساب',
                'ordering': ['template_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WhatsAppMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('individual', 'فردية'), ('group', 'جماعية'), ('overdue', 'للمتأخرين')], max_length=20, verbose_name='نوع الرسالة')),
                ('message_content', models.TextField(verbose_name='محتوى الرسالة')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('failed', 'فشلت')], default='draft', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('recipients', models.ManyToManyField(to='crm.customer', verbose_name='المستقبلين')),
            ],
            options={
                'verbose_name': 'رسالة واتساب',
                'verbose_name_plural': 'رسائل الواتساب',
                'ordering': ['-created_at'],
            },
        ),
    ]
