# Generated by Django 5.2.3 on 2025-07-02 05:24

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('phone', models.CharField(max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('currency', models.CharField(default='ريال سعودي', max_length=10, verbose_name='العملة')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company/', verbose_name='الشعار')),
                ('invoice_terms', models.TextField(blank=True, null=True, verbose_name='شروط الفاتورة')),
            ],
            options={
                'verbose_name': 'إعدادات الشركة',
                'verbose_name_plural': 'إعدادات الشركة',
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('phone', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الجوال')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Debt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('paid', 'مدفوع'), ('overdue', 'متأخر'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='debts', to='crm.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'دين',
                'verbose_name_plural': 'الديون',
                'ordering': ['-created_date'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='invoices/', verbose_name='ملف PDF')),
                ('debt', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='invoice', to='crm.debt', verbose_name='الدين')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-created_date'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')),
                ('payment_method', models.CharField(choices=[('cash', 'نقداً'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان'), ('other', 'أخرى')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('debt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='crm.debt', verbose_name='الدين')),
                ('received_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='استلم بواسطة')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-payment_date'],
            },
        ),
    ]
