{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}قائمة العملاء - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .btn-whatsapp {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: 1px solid #25D366;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
    }

    .btn-whatsapp:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        border-color: #128C7E;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }

    .btn-whatsapp:focus {
        box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        قائمة العملاء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'customer_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إضافة عميل جديد
            </a>
            {% if user.is_superuser %}
            <a href="{% url 'customer_import_excel' %}" class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i>رفع من Excel
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- البحث -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="البحث بالاسم أو الهاتف أو البريد..." 
                   value="{{ search_query }}">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
</div>

<!-- قائمة العملاء -->
<div class="card">
    <div class="card-body">
        {% if customers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>رقم الصفحة</th>
                        <th>إجمالي الديون</th>
                        <th>المبلغ المتبقي</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers %}
                    <tr>
                        <td>
                            <strong>{{ customer.name }}</strong>
                        </td>
                        <td>
                            <i class="fas fa-phone me-1"></i>
                            {{ customer.phone }}
                        </td>
                        <td>
                            {% if customer.email %}
                                <i class="fas fa-envelope me-1"></i>
                                {{ customer.email }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.page_number %}
                                <span class="badge bg-secondary">{{ customer.page_number }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ customer.total_debt|currency }}
                            </span>
                        </td>
                        <td>
                            {% if customer.remaining_debt > 0 %}
                                <span class="badge bg-warning">
                                    {{ customer.remaining_debt|currency }}
                                </span>
                            {% else %}
                                <span class="badge bg-success">مسدد</span>
                            {% endif %}
                        </td>
                        <td>{{ customer.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'customer_detail' customer.id %}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'customer_edit' customer.id %}"
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'whatsapp_send_individual' customer.id %}"
                                   class="btn btn-whatsapp" title="إرسال واتساب">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                {% if user.is_superuser %}
                                <a href="{% url 'customer_delete' customer.id %}" 
                                   class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        {% if customers.has_other_pages %}
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination justify-content-center">
                {% if customers.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ customers.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ customers.number }} من {{ customers.paginator.num_pages }}
                    </span>
                </li>

                {% if customers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ customers.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ customers.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عملاء</h5>
            {% if search_query %}
                <p class="text-muted">لم يتم العثور على نتائج للبحث "{{ search_query }}"</p>
                <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">عرض جميع العملاء</a>
            {% else %}
                <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                <a href="{% url 'customer_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
