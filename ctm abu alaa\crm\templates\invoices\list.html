{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}قائمة الفواتير - محلات أبو علاء{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-invoice me-2"></i>
        قائمة الفواتير
    </h1>
</div>

<!-- الفلاتر والبحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="رقم الفاتورة أو اسم العميل..." value="{{ search_query }}">
            </div>
            
            <div class="col-md-3">
                <label for="customer" class="form-label">العميل</label>
                <select class="form-select" id="customer" name="customer">
                    <option value="">جميع العملاء</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}" {% if customer_filter == customer.id|stringformat:"s" %}selected{% endif %}>
                        {{ customer.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{% url 'invoice_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الفواتير -->
<div class="card">
    <div class="card-body">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>المبلغ</th>
                        <th>تاريخ الإصدار</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>
                            <strong>{{ invoice.invoice_number }}</strong>
                        </td>
                        <td>
                            <a href="{% url 'customer_detail' invoice.debt.customer.id %}" class="text-decoration-none">
                                <strong>{{ invoice.debt.customer.name }}</strong>
                            </a>
                            <br>
                            <small class="text-muted">{{ invoice.debt.customer.phone }}</small>
                        </td>
                        <td>
                            <strong>{{ invoice.debt.amount|currency }}</strong>
                        </td>
                        <td>{{ invoice.created_date|date:"Y-m-d" }}</td>
                        <td>
                            {{ invoice.debt.due_date|date:"Y-m-d" }}
                            {% if invoice.debt.is_overdue %}
                                <br><span class="badge bg-danger">متأخر</span>
                            {% elif invoice.debt.is_due_soon %}
                                <br><span class="badge bg-warning">مستحق قريباً</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.debt.status == 'paid' %}
                                <span class="badge bg-success">مدفوعة</span>
                            {% elif invoice.debt.status == 'overdue' %}
                                <span class="badge bg-danger">متأخرة</span>
                            {% elif invoice.debt.status == 'active' %}
                                <span class="badge bg-primary">نشطة</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ invoice.debt.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'invoice_detail' invoice.id %}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'invoice_pdf' invoice.id %}" 
                                   class="btn btn-outline-danger" title="تحميل PDF" target="_blank">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <a href="{% url 'debt_detail' invoice.debt.id %}" 
                                   class="btn btn-outline-secondary" title="عرض الدين">
                                    <i class="fas fa-money-bill-wave"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        {% if invoices.has_other_pages %}
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination justify-content-center">
                {% if invoices.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ invoices.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ invoices.number }} من {{ invoices.paginator.num_pages }}
                    </span>
                </li>

                {% if invoices.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ invoices.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ invoices.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد فواتير</h5>
            {% if search_query or status_filter or customer_filter %}
                <p class="text-muted">لم يتم العثور على نتائج للفلاتر المحددة</p>
                <a href="{% url 'invoice_list' %}" class="btn btn-outline-secondary">عرض جميع الفواتير</a>
            {% else %}
                <p class="text-muted">لا توجد فواتير مصدرة حتى الآن</p>
                <a href="{% url 'debt_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة دين جديد
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
